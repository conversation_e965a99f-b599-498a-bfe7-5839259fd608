#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量网址打开器
分批次打开CSV文件中的网址，每批25个，等待用户确认后继续下一批
"""

import pandas as pd
import webbrowser
import time
import re
import os
from urllib.parse import urlparse


def is_valid_url(url):
    """
    检查URL是否有效
    
    Args:
        url (str): 要检查的URL
    
    Returns:
        bool: URL是否有效
    """
    try:
        result = urlparse(url.strip())
        return all([result.scheme, result.netloc])
    except:
        return False


def extract_urls_from_text(text):
    """
    从文本中提取所有URL
    
    Args:
        text (str): 包含URL的文本
    
    Returns:
        list: 提取出的URL列表
    """
    if pd.isna(text) or not isinstance(text, str):
        return []
    
    # 使用正则表达式匹配URL
    url_pattern = r'https?://[^\s\n\r"]+'
    urls = re.findall(url_pattern, text)
    
    # 过滤有效的URL
    valid_urls = [url for url in urls if is_valid_url(url)]
    
    return valid_urls


def load_urls_from_csv(csv_file_path):
    """
    从CSV文件中加载所有URL
    
    Args:
        csv_file_path (str): CSV文件路径
    
    Returns:
        list: URL列表，每个元素是 (url, source_info) 元组
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
        
        # 检查是否有data_sources列
        if 'data_sources' not in df.columns:
            print(f"警告: {csv_file_path} 中没有找到 'data_sources' 列")
            return []
        
        urls = []
        file_name = os.path.basename(csv_file_path)
        
        for index, row in df.iterrows():
            # 获取ID和data_sources
            record_id = row.get('id', f'行{index+1}')
            data_sources = row['data_sources']
            
            # 从data_sources中提取URL
            extracted_urls = extract_urls_from_text(data_sources)
            
            for url in extracted_urls:
                urls.append((url, f"{file_name} - {record_id}"))
        
        return urls
        
    except Exception as e:
        print(f"读取文件 {csv_file_path} 时出错: {e}")
        return []


def open_urls_in_batch(urls, batch_size=25):
    """
    分批次打开URL
    
    Args:
        urls (list): URL列表
        batch_size (int): 每批次的URL数量
    """
    if not urls:
        print("没有找到有效的URL")
        return
    
    total_urls = len(urls)
    total_batches = (total_urls + batch_size - 1) // batch_size
    
    print(f"总共找到 {total_urls} 个URL，将分 {total_batches} 批次打开")
    print(f"每批次打开 {batch_size} 个网页")
    print("=" * 60)
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, total_urls)
        batch_urls = urls[start_idx:end_idx]
        
        print(f"\n第 {batch_num + 1}/{total_batches} 批次")
        print(f"准备打开 {len(batch_urls)} 个网页 (URL {start_idx + 1}-{end_idx})")
        print("-" * 40)
        
        # 显示即将打开的URL
        for i, (url, source_info) in enumerate(batch_urls, 1):
            print(f"{i:2d}. {source_info}")
            print(f"    {url}")
        
        print("-" * 40)
        
        # 等待用户确认
        if batch_num == 0:
            user_input = input("按回车键开始打开第一批网页，或输入 'q' 退出: ").strip().lower()
        else:
            user_input = input("按回车键打开下一批网页，或输入 'q' 退出: ").strip().lower()
        
        if user_input == 'q':
            print("用户取消操作")
            break
        
        # 打开这批URL
        print(f"正在打开第 {batch_num + 1} 批次的网页...")
        
        for i, (url, source_info) in enumerate(batch_urls, 1):
            try:
                print(f"  打开 {i}/{len(batch_urls)}: {url}")
                webbrowser.open(url)
                # 稍微延迟，避免同时打开太多网页造成浏览器卡顿
                time.sleep(0.5)
            except Exception as e:
                print(f"  错误: 无法打开 {url} - {e}")
        
        print(f"第 {batch_num + 1} 批次完成!")
        
        # 如果不是最后一批，显示提示
        if batch_num < total_batches - 1:
            remaining_batches = total_batches - batch_num - 1
            remaining_urls = total_urls - end_idx
            print(f"还剩 {remaining_batches} 批次，{remaining_urls} 个URL")


def main():
    """主函数"""
    print("批量网址打开器")
    print("=" * 60)
    
    # CSV文件列表
    csv_files = ["南海.csv", "黄渤海.csv"]
    
    # 检查文件是否存在
    existing_files = []
    for file in csv_files:
        if os.path.exists(file):
            existing_files.append(file)
            print(f"找到文件: {file}")
        else:
            print(f"警告: 文件不存在: {file}")
    
    if not existing_files:
        print("错误: 没有找到任何CSV文件")
        return
    
    # 从所有CSV文件中加载URL
    all_urls = []
    for csv_file in existing_files:
        print(f"\n正在加载 {csv_file}...")
        urls = load_urls_from_csv(csv_file)
        all_urls.extend(urls)
        print(f"从 {csv_file} 中提取了 {len(urls)} 个URL")
    
    if not all_urls:
        print("没有找到任何有效的URL")
        return
    
    print(f"\n总共加载了 {len(all_urls)} 个URL")
    
    # 询问用户是否要查看URL列表
    show_list = input("\n是否要查看所有URL列表？(y/N): ").strip().lower()
    if show_list == 'y':
        print("\n所有URL列表:")
        print("=" * 60)
        for i, (url, source_info) in enumerate(all_urls, 1):
            print(f"{i:3d}. {source_info}")
            print(f"     {url}")
        print("=" * 60)
    
    # 询问批次大小
    try:
        batch_size_input = input(f"\n每批次打开多少个网页？(默认25): ").strip()
        batch_size = int(batch_size_input) if batch_size_input else 25
        if batch_size <= 0:
            batch_size = 25
    except ValueError:
        batch_size = 25
    
    print(f"将使用批次大小: {batch_size}")
    
    # 开始分批次打开URL
    open_urls_in_batch(all_urls, batch_size)
    
    print("\n程序结束")


if __name__ == "__main__":
    main()
